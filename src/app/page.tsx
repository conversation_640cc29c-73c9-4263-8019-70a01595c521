
//This page uses Local persistence. Do Not Edit This!
'use client'
import { Tldraw, createShapeId, TLUiComponents, TLEditorComponents, track, useEditor  } from 'tldraw'
import 'tldraw/tldraw.css'
import { ResearchResultsUtil } from '@/shapes/ResearchResults'
import { DesignSpecUtil } from '@/shapes/DesignSpec'
import { HTMLDesignMockupsUtil } from '@/shapes/HTMLDesignMockups'
import { ShadcnHydridMockupContainerUtil } from '@/shapes/ShadcnHydridMockupContainer'
import { HybridDesignMockupsUtil } from '@/shapes/HybridDesignMockups'
import { AiChatView } from '@/components/AiChatView'
import IntroSection from '@/components/IntroSection'
import { useCallback, useRef } from 'react'
import '@/app/style_overrides.css'
import { Iterate } from '@/components/Iterate'
import RunnerComponent from '@/components/magicui/runner'
import { WelcomeComponent } from '@/components/WelcomeComponent'

const customShapeUtils = [
  ResearchResultsUtil, 
  DesignSpecUtil, 
  HTMLDesignMockupsUtil, 
  ShadcnHydridMockupContainerUtil,
  HybridDesignMockupsUtil,
]



export default function Home() {

  
  const ContextToolbarComponent = track(() => {
    const editor = useEditor()
    const showToolbar = editor.isIn('select.idle')
    if (!showToolbar) return null

    const selectedShapes = editor.getSelectedShapes()
    if (selectedShapes.length !== 1) return null // Only show for single selection
    
    const selectedShape = selectedShapes[0]
    if (selectedShape.type !== 'html-design-mockups' && selectedShape.type !== 'hybrid-design-mockups') return null // Only show for HTML design mockups

    // Check if shape is loading
    const isLoading = (selectedShape as any).props.loading
    if (isLoading) return null // Don't show toolbar while loading

    const selectionRotatedPageBounds = editor.getSelectionRotatedPageBounds()
    if (!selectionRotatedPageBounds) return null
  
    const pageCoordinates = editor.pageToViewport(selectionRotatedPageBounds.point)
    const shapeId = selectedShape.id
    const html = (selectedShape as any).props.html // Get HTML from shape props
  
    return (
      <div
        style={{
          position: 'absolute',
          pointerEvents: 'all',
          top: pageCoordinates.y - 80,
          left: pageCoordinates.x + 0,
          width: selectionRotatedPageBounds.width * editor.getZoomLevel(),
          //width: 200,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        onPointerDown={(e) => e.stopPropagation()}
      >
        <div
          style={{
            borderRadius: 8,
            display: 'flex',
            //boxShadow: '0 0 0 1px rgba(0,0,0,0.1), 0 4px 8px rgba(0,0,0,0.1)',
            //background: 'var(--color-panel)',
            background: '#fff',
            width: 'fit-content',
            alignItems: 'center',
          }}
        >
          <div className="flex flex-row gap-2 min-h-[44px] max-h-[44px] px-4 overflow-hidden rounded-lg shadow-lg bg-white dark:bg-gray-800 items-center justify-between"
            style={{ width: 'clamp(300px, 90vw, 600px)'}}>
            <span className="text-sm font-bold">Mockup</span>
            <div className="flex flex-row gap-2">
              <Iterate currentHtml={html} shapeId={shapeId} />
            </div>
          </div>
        </div>
      </div>
    )
  })

  const components: Partial<TLUiComponents & TLEditorComponents> = {
    StylePanel: null,
    HelpMenu: null,
    //MenuPanel: null,
    ActionsMenu: null,
    Toolbar: null,
  }

  const components2: TLEditorComponents = {
    InFrontOfTheCanvas: ContextToolbarComponent,
    OnTheCanvas: WelcomeComponent
  }

  const allComponents = {
    ...components,
    ...components2,
    
  }

  return (
    <div style={{ position: 'fixed', inset: 0 }}>
      <Tldraw
        shapeUtils={customShapeUtils}
        persistenceKey="ux-assistant-canvas-ux"
        components={allComponents}
        onMount={(editor) => {
					editor.updateInstanceState({ isGridMode: false,  })
         //editor.createShape({ type: 'html-design-mockups', x: 100, y: 100, props: { html: '', w: 400, h: 400 } })
          
				}}
      >
        <AiChatView />
      </Tldraw>
    </div>
  )
}
