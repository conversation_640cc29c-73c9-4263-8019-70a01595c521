export const designSpecExample = `
# UI Specification: Sign Up - Vitality Tracker

This document details the UI for the Vitality Tracker mobile sign-up page.

## 1. Page: Sign Up - Vitality Tracker

**Purpose:** Allows new users to create an account to access the Vitality Tracker application.
**Container:**
*   **Background Color:** \`#FFFFFF\` (White)
*   **Padding:** \`24px\` horizontal padding from screen edges. Vertical padding as needed for content flow, starting \`40px\` from the top safe area.

## 2. Header

**Purpose:** Identifies the application.
**Type:** Text/App Name
**Content:** \`Vitality Tracker\`
**Visual Properties:**
*   **Font:** System Font, Bold
*   **Font Size:** \`28px\`
*   **Font Color:** \`#1A1A1A\` (Near Black)
*   **Alignment:** Center horizontally
*   **Spacing:** \`40px\` below the top edge of the container.

## 3. Sign-Up Title

**Purpose:** Clearly states the purpose of the page.
**Type:** Text
**Content:** \`Create Your Account\`
**Visual Properties:**
*   **Font:** System Font, Semibold
*   **Font Size:** \`24px\`
*   **Font Color:** \`#1A1A1A\` (Near Black)
*   **Spacing:** \`32px\` below the Header.

## 4. Sign-Up Description

**Purpose:** Provides a brief value proposition.
**Type:** Text
**Content:** \`Start tracking your health journey today.\`
**Visual Properties:**
*   **Font:** System Font, Regular
*   **Font Size:** \`16px\`
*   **Font Color:** \`#5E5E5E\` (Dark Gray)
*   **Spacing:** \`8px\` below the Sign-Up Title.

## 5. Email Input Field

**Purpose:** Allows the user to enter their email address.
**Container:**
*   **Background Color:** \`#F5F5F5\` (Light Gray)
*   **Border Radius:** \`8px\`
*   **Padding:** \`14px\` vertical, \`16px\` horizontal.
*   **Spacing:** \`32px\` below the Sign-Up Description.

**Element: Label**
*   **Purpose:** Identifies the input field.
*   **Content:** \`Email Address\`
*   **Visual Properties:**
*   **Font:** System Font, Medium
*   **Font Size:** \`14px\`
*   **Font Color:** \`#1A1A1A\` (Near Black)
*   **Spacing:** \`0px\` relative to the top of the input container (placed above the input text area).

**Element: Text Input Area**
*   **Purpose:** User enters email here.
*   **Content:** (User Input)
*   **Placeholder Content:** \`e.g., <EMAIL>\`
*   **Visual Properties:**
*   **Font:** System Font, Regular
*   **Font Size:** \`16px\`
*   **Font Color (Input):** \`#1A1A1A\` (Near Black)
*   **Font Color (Placeholder):** \`#A9A9A9\` (Light Gray)
*   **Spacing:** \`4px\` below the Label.
*   **Keyboard Type:** \`email-address\`
*   **Autocomplete:** \`email\`
*   **Border:** \`1px\` solid \`#E0E0E0\` (Very Light Gray) in default state. \`1px\` solid \`#007AFF\` (Primary Blue) in focus state.
*   **Error State Example:** If validation fails, border changes to \`1px\` solid \`#FF3B30\` (Red), and an error message appears below the input field.

**Element: Error Message (Conditional)**
*   **Purpose:** Informs the user of input errors.
*   **Content Example:** \`Please enter a valid email address.\`
*   **Visual Properties:**
*   **Font:** System Font, Regular
*   **Font Size:** \`12px\`
*   **Font Color:** \`#FF3B30\` (Red)
*   **Spacing:** \`4px\` below the Email Input Field container.

## 6. Password Input Field

**Purpose:** Allows the user to create a password.
**Container:**
*   **Background Color:** \`#F5F5F5\` (Light Gray)
*   **Border Radius:** \`8px\`
*   **Padding:** \`14px\` vertical, \`16px\` horizontal.
*   **Spacing:** \`16px\` below the Email Input Field (or its error message if present).

**Element: Label**
*   **Purpose:** Identifies the input field.
*   **Content:** \`Password\`
*   **Visual Properties:**
*   **Font:** System Font, Medium
*   **Font Size:** \`14px\`
*   **Font Color:** \`#1A1A1A\` (Near Black)
*   **Spacing:** \`0px\` relative to the top of the input container (placed above the input text area).

**Element: Text Input Area**
*   **Purpose:** User enters password here.
*   **Content:** (User Input)
*   **Placeholder Content:** \`create a strong password\`
*   **Visual Properties:**
*   **Font:** System Font, Regular
*   **Font Size:** \`16px\`
*   **Font Color (Input):** \`#1A1A1A\` (Near Black)
*   **Font Color (Placeholder):** \`#A9A9A9\` (Light Gray)
*   **Spacing:** \`4px\` below the Label.
*   **Keyboard Type:** \`default\`
*   **Secure Text Entry:** Enabled (dots instead of characters).
*   **Autocomplete:** \`new-password\`
*   **Border:** \`1px\` solid \`#E0E0E0\` (Very Light Gray) in default state. \`1px\` solid \`#007AFF\` (Primary Blue) in focus state.
*   **Error State Example:** If validation fails, border changes to \`1px\` solid \`#FF3B30\` (Red), and an error message appears below the input field.

**Element: Password Toggle Icon**
*   **Purpose:** Toggles password visibility.
*   **Type:** Icon Button
*   **Icon:** Eye icon (\`eye\` or \`eye-slash\` from a standard icon library like Font Awesome or Material Icons)
*   **Visual Properties:**
*   **Color:** \`#A9A9A9\` (Light Gray) in default state, \`#5E5E5E\` (Dark Gray) when active/toggled.
*   **Size:** \`20px\` x \`20px\`
*   **Position:** Aligned to the right edge of the input field, vertically centered within the input text area height.

**Element: Error Message (Conditional)**
*   **Purpose:** Informs the user of input errors.
*   **Content Example:** \`Password must be at least 8 characters.\`
*   **Visual Properties:**
*   **Font:** System Font, Regular
*   **Font Size:** \`12px\`
*   **Font Color:** \`#FF3B30\` (Red)
*   **Spacing:** \`4px\` below the Password Input Field container.

## 7. Create Account Button (Primary CTA)

**Purpose:** Submits the sign-up form.
**Type:** Button
**Content:** \`Create Account\`
**Visual Properties:**
*   **Background Color:** \`#007AFF\` (Primary Blue)
*   **Text Color:** \`#FFFFFF\` (White)
*   **Font:** System Font, Semibold
*   **Font Size:** \`18px\`
*   **Border Radius:** \`8px\`
*   **Padding:** \`16px\` vertical. Full width.
*   **Spacing:** \`32px\` below the Password Input Field (or its error message if present).
*   **State (Disabled):** Background Color \`#B0C4DE\` (Light Steel Blue), Text Color \`#E0E0E0\` (Very Light Gray). Button should be disabled if fields are empty or invalid.

## 8. Separator

**Purpose:** Separates the email/password form from alternative sign-up options.
**Type:** Text with lines
**Content:** \`or continue with\`
**Visual Properties:**
*   **Text Font:** System Font, Regular, \`14px\`, \`#A9A9A9\` (Light Gray)
*   **Lines:** \`1px\` solid \`#E0E0E0\` (Very Light Gray), extending horizontally to the left and right of the text.
*   **Spacing:** \`24px\` below the Create Account Button.

## 9. Social Sign-Up Buttons

**Purpose:** Provides alternative sign-up methods using third-party accounts.
**Layout:** Horizontal stack, centered, with spacing between buttons.
**Spacing:** \`16px\` below the Separator.

**Element: Google Sign-Up Button**
*   **Purpose:** Sign up via Google account.
*   **Type:** Button
*   **Content:**
*   **Icon:** Google logo (\`32px\` x \`32px\`) - full color.
*   **Text:** (No text, just icon)
*   **Visual Properties:**
*   **Background Color:** \`#FFFFFF\` (White)
*   **Border:** \`1px\` solid \`#E0E0E0\` (Very Light Gray)
*   **Border Radius:** \`8px\`
*   **Size:** \`48px\` x \`48px\` (Icon centered within the button)
*   **Spacing:** \`16px\` horizontal spacing from the Apple button.

**Element: Apple Sign-Up Button**
*   **Purpose:** Sign up via Apple account.
*   **Type:** Button
*   **Content:**
*   **Icon:** Apple logo (\`32px\` x \`32px\`) - black fill.
*   **Text:** (No text, just icon)
*   **Visual Properties:**
*   **Background Color:** \`#FFFFFF\` (White)
*   **Border:** \`1px\` solid \`#E0E0E0\` (Very Light Gray)
*   **Border Radius:** \`8px\`
*   **Size:** \`48px\` x \`48px\` (Icon centered within the button)
*   **Spacing:** \`16px\` horizontal spacing from the Google button.

*(Note: Other social options like Facebook could be added here, following a similar pattern).*

## 10. Login Link

**Purpose:** Provides navigation for users who already have an account.
**Type:** Text Link
**Content:** \`Already have an account? \` + **Link Text:** \`Log In\`
**Visual Properties:**
*   **Text Font:** System Font, Regular, \`14px\`, \`#5E5E5E\` (Dark Gray)
*   **Link Text Font:** System Font, Semibold, \`14px\`, \`#007AFF\` (Primary Blue)
*   **Spacing:** \`40px\` below the Social Sign-Up Buttons.
*   **Alignment:** Center horizontally.
`;


export const htmlDesignExample = `
<div style="width: 400px; min-width: 400px; max-width: 400px; background-color: #FFFFFF; padding: 40px 24px 40px 24px; box-sizing: border-box; display: flex; flex-direction: column; align-items: stretch;">
  <div style="font-weight: bold; font-size: 28px; color: #1A1A1A; margin-bottom: 32px; text-align: center;">
    Vitality Tracker
  </div>
  <div style="font-weight: 600; font-size: 24px; color: #1A1A1A; margin-bottom: 8px;">
    Create Your Account
  </div>
  <div style="font-weight: normal; font-size: 16px; color: #5E5E5E; margin-bottom: 32px;">
    Start tracking your health journey today.
  </div>

  <div style="background-color: #F5F5F5; border-radius: 8px; padding: 14px 16px; margin-top: 0; margin-bottom: 16px; border: 1px solid #E0E0E0; box-sizing: border-box; display: flex; flex-direction: column;">
    <label for="email" style="font-weight: 500; font-size: 14px; color: #1A1A1A; margin-bottom: 4px;">
      Email Address
    </label>
    <input id="email" type="text" placeholder="e.g., <EMAIL>" style="font-weight: normal; font-size: 16px; color: #1A1A1A; border: none; outline: none; background: none; padding: 0; margin: 0; width: 100%;">
  </div>

  <div style="background-color: #F5F5F5; border-radius: 8px; padding: 14px 16px; margin-top: 16px; margin-bottom: 32px; border: 1px solid #E0E0E0; box-sizing: border-box; display: flex; flex-direction: column;">
    <label for="password" style="font-weight: 500; font-size: 14px; color: #1A1A1A; margin-bottom: 4px;">
      Password
    </label>
    <div style="display: flex; align-items: center;">
      <input id="password" type="password" placeholder="create a strong password" style="font-weight: normal; font-size: 16px; color: #1A1A1A; border: none; outline: none; background: none; padding: 0; margin: 0; flex-grow: 1;">
      <button style="background: none; border: none; padding: 0; cursor: pointer; margin-left: 8px; color: #A9A9A9; font-size: 20px; line-height: 1; display: flex; align-items: center;">
        👁️
      </button>
    </div>
  </div>

  <button style="background-color: #007AFF; color: #FFFFFF; font-weight: 600; font-size: 18px; border-radius: 8px; padding: 16px 0; width: 100%; border: none; cursor: pointer; margin-top: 0; margin-bottom: 24px;">
    Create Account
  </button>

  <div style="display: flex; align-items: center; margin-top: 24px; margin-bottom: 16px;">
    <div style="flex-grow: 1; height: 1px; background-color: #E0E0E0; margin: 0 8px;"></div>
    <span style="font-weight: normal; font-size: 14px; color: #A9A9A9; white-space: nowrap;">or continue with</span>
    <div style="flex-grow: 1; height: 1px; background-color: #E0E0E0; margin: 0 8px;"></div>
  </div>

  <div style="display: flex; justify-content: center; margin-top: 16px; margin-bottom: 40px;">
    <button style="background-color: #FFFFFF; border: 1px solid #E0E0E0; border-radius: 8px; width: 48px; height: 48px; display: flex; justify-content: center; align-items: center; cursor: pointer; margin-right: 16px; padding: 0;">
      <div style="width: 32px; height: 32px; background-color: transparent;"></div>
    </button>
    <button style="background-color: #FFFFFF; border: 1px solid #E0E0E0; border-radius: 8px; width: 48px; height: 48px; display: flex; justify-content: center; align-items: center; cursor: pointer; padding: 0;">
      <div style="width: 32px; height: 32px; background-color: transparent;"></div>
    </button>
  </div>

  <div style="font-size: 14px; color: #5E5E5E; text-align: center; margin-top: 40px;">
    <span style="font-weight: normal;">Already have an account? </span>
    <a href="#" style="font-weight: 600; color: #007AFF; text-decoration: none;">Log In</a>
  </div>
</div>
`;